{"cells": [{"cell_type": "code", "execution_count": 2, "id": "496fb30d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error tracking the completion usage: 'NoneType' object has no attribute 'completion_tokens'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["GeneratorOutput(id=None, input='<START_OF_SYSTEM_PROMPT>\\nYou are a helpful assistant.\\n<END_OF_SYSTEM_PROMPT>\\n<START_OF_USER_PROMPT>\\nwho are you ?\\n<END_OF_USER_PROMPT>\\n', data='Hi! I’m ChatGPT, a large language model created by OpenAI. I’m here to answer questions, explain things, help with writing, brainstorm ideas, and more. How can I help you today?', thinking=None, tool_use=None, images=None, error=None, usage=CompletionUsage(completion_tokens=None, prompt_tokens=None, total_tokens=None), raw_response='Hi! I’m ChatGPT, a large language model created by OpenAI. I’m here to answer questions, explain things, help with writing, brainstorm ideas, and more. How can I help you today?', api_response=ChatCompletion(id='chatcmpl-C3JIBsRx11QF9Bl3QfQXcemiWr4MV', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='Hi! I’m ChatGPT, a large language model created by OpenAI. I’m here to answer questions, explain things, help with writing, brainstorm ideas, and more. How can I help you today?', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754905181, model='gpt-5-nano', object='chat.completion', service_tier=None, system_fingerprint=None, usage=None), metadata=None)\n"]}], "source": ["\n", "from backend.api.clients.litellm_client import LLMClient\n", "if __name__ == \"__main__\":\n", "    import adalflow as adal\n", "\n", "    # setup env or pass the api_key\n", "    from adalflow.utils import setup_env\n", "\n", "    # setup_env()\n", "\n", "    openai_llm = adal.Generator(\n", "        model_client=LLMClient(base_url=\"http://localhost:4000\", api_key=\"sk-i92HUrwROSClBVKR-NCARg\"), model_kwargs={\"model\": \"gpt-5-nano\"}\n", "    )\n", "    resopnse = openai_llm(prompt_kwargs={\"input_str\": \"who are you ?\"})\n", "    print(resopnse)"]}, {"cell_type": "code", "execution_count": 5, "id": "9426f4f2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error tracking the completion usage: 'NoneType' object has no attribute 'completion_tokens'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["GeneratorOutput(id=None, input='<START_OF_SYSTEM_PROMPT>\\nYou are a helpful assistant.\\n<END_OF_SYSTEM_PROMPT>\\n<START_OF_USER_PROMPT>\\nwho am i ?\\n<END_OF_USER_PROMPT>\\n', data='I don’t know who you are—there isn’t any information about you in this chat, and I can’t identify people. You’re simply the person I’m chatting with here.\\n\\nIf you’d like, we can do one of these:\\n- Quick self-reflection to describe who you are (values, strengths, goals).\\n- Write a short personal bio or elevator pitch you can use online.\\n- A fun list of interests and traits to summarize your identity.\\n\\nTell me which option you prefer, or share a bit about yourself and I’ll tailor it.', thinking=None, tool_use=None, images=None, error=None, usage=CompletionUsage(completion_tokens=None, prompt_tokens=None, total_tokens=None), raw_response='I don’t know who you are—there isn’t any information about you in this chat, and I can’t identify people. You’re simply the person I’m chatting with here.\\n\\nIf you’d like, we can do one of these:\\n- Quick self-reflection to describe who you are (values, strengths, goals).\\n- Write a short personal bio or elevator pitch you can use online.\\n- A fun list of interests and traits to summarize your identity.\\n\\nTell me which option you prefer, or share a bit about yourself and I’ll tailor it.', api_response=ChatCompletion(id='chatcmpl-C3JQy9MHTrcukHo4dAVmXLj1EGx2A', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='I don’t know who you are—there isn’t any information about you in this chat, and I can’t identify people. You’re simply the person I’m chatting with here.\\n\\nIf you’d like, we can do one of these:\\n- Quick self-reflection to describe who you are (values, strengths, goals).\\n- Write a short personal bio or elevator pitch you can use online.\\n- A fun list of interests and traits to summarize your identity.\\n\\nTell me which option you prefer, or share a bit about yourself and I’ll tailor it.', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1754905728, model='gpt-5-nano', object='chat.completion', service_tier=None, system_fingerprint=None, usage=None), metadata=None)\n"]}], "source": ["\n", "from backend.api.clients.litellm_client import LLMClient\n", "if __name__ == \"__main__\":\n", "\n", "    # setup env or pass the api_key\n", "\n", "    # setup_env()\n", "\n", "    openai_llm = adal.Generator(\n", "        model_client=LLMClient(base_url=\"http://localhost:4000\", api_key=\"sk-i92HUrwROSClBVKR-NCARg\"), model_kwargs={\"model\": \"gpt-5-nano\"}\n", "    )\n", "    resopnse = openai_llm(prompt_kwargs={\"input_str\": \"who am i ?\", \"stream\": True})\n", "    print(resopnse)"]}], "metadata": {"kernelspec": {"display_name": "deepwiki-engine", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}