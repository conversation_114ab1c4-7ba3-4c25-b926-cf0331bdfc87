# generate_litellm_key.py
import os
import json
import uuid
import requests

BASE_URL   = os.getenv("LITELLM_BASE_URL", "http://localhost:4000")
MASTER_KEY = os.getenv("PROXY_MASTER_KEY", "sk-8752d703207ecf535933eefea6e8672adc5b61e0d4faaed9135ba5ad522380e4")

url = f"{BASE_URL}/key/generate"
headers = {
    "Authorization": f"Bearer {MASTER_KEY}",
    "Content-Type": "application/json",
}

# Tạo alias kèm UUID
alias_with_uuid = f"random-{uuid.uuid4()}"

payload = {
    "key_alias": alias_with_uuid,
    "models": ["gpt-5-nano"],
    "key_type": "default",  # "default" | "llm_api" | "management" | "read_only"
}

resp = requests.post(url, headers=headers, data=json.dumps(payload))
resp.raise_for_status()
data = resp.json()

print("=== LiteLLM Key Generated ===")
print("Key:", data.get("key"))   # sk-... (ngẫu nhiên)
print("Alias:", data.get("key_alias"))
print("Models:", data.get("models"))
print("Response JSON:")
print(json.dumps(data, indent=2, ensure_ascii=False))
