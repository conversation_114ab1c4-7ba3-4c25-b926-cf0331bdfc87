# backend/Dockerfile
FROM ghcr.io/astral-sh/uv:debian-slim

WORKDIR /app

# # --- Install runtime deps first (for healthcheck, etc.)
# RUN apt-get update && apt-get install -y --no-install-recommends curl \
#     && rm -rf /var/lib/apt/lists/*

# --- 1) Copy project metadata to leverage build cache
# Nếu bạn có uv.lock thì copy luôn để cache tốt hơn
COPY pyproject.toml ./ 
COPY uv.lock ./ 

# --- 2) Cài deps
# Thử sync theo pyproject/lock; nếu (chưa có lock hoặc pyproject thiếu) thì fallback sang requirements.txt
# --system: cài vào site-packages của hệ thống
RUN (uv sync --frozen --no-dev --system) || true 

# --- 3) Copy source code
COPY ./api/ ./api/

ENV PORT=8001
EXPOSE 8001

# --- 4) Run app
CMD ["uv","run", "uvicorn", "api.api:app", "--host", "0.0.0.0", "--port", "8001"]
