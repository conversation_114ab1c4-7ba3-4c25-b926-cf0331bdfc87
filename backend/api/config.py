import json
import logging
import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

from api.clients.openai_client import OpenAIClient


class DeepWikiSettings(BaseModel):
    """
    Centralized configuration management using Pydantic for type safety and validation.
    All environment variables are managed here with proper defaults and validation.
    """

    # API Keys
    openai_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    openrouter_api_key: Optional[str] = None

    # AWS Configuration
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_region: Optional[str] = None
    aws_role_arn: Optional[str] = None

    # LiteLLM Configuration
    litellm_client_api_key: Optional[str] = None
    litellm_base_url: str = 'http://localhost:4000'

    # Wiki Authentication
    wiki_auth_mode: bool = False
    wiki_auth_code: str = ''

    # Model Configuration
    default_temperature: float = Field(0.3, ge=0.0, le=2.0)

    # Embedder Configuration
    embedder_model: str = 'text-embedding-3-small'
    embedder_dimensions: int = Field(256, gt=0)
    embedder_encoding_format: str = 'float'

    # Configuration Directory
    config_dir: Optional[str] = None

    # Parallel Processing Configuration
    parallel_processing_enabled: bool = True
    max_document_workers: int = Field(4, ge=1, le=16)
    max_file_workers: int = Field(6, ge=1, le=16)
    max_context_workers: int = Field(8, ge=1, le=16)
    min_documents_for_parallel: int = Field(10, ge=1)

    # Performance Configuration
    enable_performance_logging: bool = True
    adaptive_worker_scaling: bool = True
    performance_threshold: float = Field(0.1, ge=0.0)

    # Context Management
    max_context_length: int = Field(8000, ge=1000)
    context_chunk_size: int = Field(2000, ge=100)

    @classmethod
    def from_env(cls) -> 'DeepWikiSettings':
        """Create settings instance from environment variables."""
        def get_env_bool(key: str, default: bool = False) -> bool:
            """Parse boolean from environment variable."""
            value = os.environ.get(key, '').lower()
            if value in ['true', '1', 't', 'yes', 'on']:
                return True
            elif value in ['false', '0', 'f', 'no', 'off']:
                return False
            return default

        def get_env_int(key: str, default: int) -> int:
            """Parse integer from environment variable."""
            try:
                return int(os.environ.get(key, str(default)))
            except ValueError:
                return default

        def get_env_float(key: str, default: float) -> float:
            """Parse float from environment variable."""
            try:
                return float(os.environ.get(key, str(default)))
            except ValueError:
                return default

        return cls(
            # API Keys
            openai_api_key=os.environ.get('OPENAI_API_KEY'),
            google_api_key=os.environ.get('GOOGLE_API_KEY'),
            openrouter_api_key=os.environ.get('OPENROUTER_API_KEY'),

            # AWS Configuration
            aws_access_key_id=os.environ.get('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.environ.get('AWS_SECRET_ACCESS_KEY'),
            aws_region=os.environ.get('AWS_REGION'),
            aws_role_arn=os.environ.get('AWS_ROLE_ARN'),

            # LiteLLM Configuration
            litellm_client_api_key=(
                os.environ.get('LITELLM_CLIENT_API_KEY') or
                os.environ.get('LITELLM_API_KEY')
            ),
            litellm_base_url=(
                os.environ.get('LITELLM_BASE_URL') or
                os.environ.get('OPENAI_BASE_URL') or
                'http://localhost:4000'
            ),

            # Wiki Authentication
            wiki_auth_mode=get_env_bool('DEEPWIKI_AUTH_MODE', False),
            wiki_auth_code=os.environ.get('DEEPWIKI_AUTH_CODE', ''),

            # Model Configuration
            default_temperature=get_env_float('DEFAULT_TEMPERATURE', 0.3),

            # Embedder Configuration
            embedder_model=os.environ.get('EMBEDDER_MODEL', 'text-embedding-3-small'),
            embedder_dimensions=get_env_int('EMBEDDER_DIMENSIONS', 256),
            embedder_encoding_format=os.environ.get('EMBEDDER_ENCODING_FORMAT', 'float'),

            # Configuration Directory
            config_dir=os.environ.get('DEEPWIKI_CONFIG_DIR'),

            # Parallel Processing Configuration
            parallel_processing_enabled=get_env_bool('PARALLEL_PROCESSING_ENABLED', True),
            max_document_workers=get_env_int('MAX_DOCUMENT_WORKERS', 4),
            max_file_workers=get_env_int('MAX_FILE_WORKERS', 6),
            max_context_workers=get_env_int('MAX_CONTEXT_WORKERS', 8),
            min_documents_for_parallel=get_env_int('MIN_DOCUMENTS_FOR_PARALLEL', 10),

            # Performance Configuration
            enable_performance_logging=get_env_bool('ENABLE_PERFORMANCE_LOGGING', True),
            adaptive_worker_scaling=get_env_bool('ADAPTIVE_WORKER_SCALING', True),
            performance_threshold=get_env_float('PERFORMANCE_THRESHOLD', 0.1),

            # Context Management
            max_context_length=get_env_int('MAX_CONTEXT_LENGTH', 8000),
            context_chunk_size=get_env_int('CONTEXT_CHUNK_SIZE', 2000),
        )

    def __init__(self, **kwargs):
        """Initialize settings with custom validation."""
        super().__init__(**kwargs)

        # Custom validation for base URL
        if not self.litellm_base_url.startswith(('http://', 'https://')):
            self.litellm_base_url = f'http://{self.litellm_base_url}'
        self.litellm_base_url = self.litellm_base_url.rstrip('/')

    def setup_environment_variables(self):
        """Set up environment variables for backward compatibility and client libraries."""
        # Set up OpenAI SDK environment
        if self.litellm_client_api_key:
            os.environ["OPENAI_API_KEY"] = self.litellm_client_api_key
        elif self.openai_api_key:
            os.environ["OPENAI_API_KEY"] = self.openai_api_key

        # Set base URL for OpenAI SDK
        os.environ.setdefault("OPENAI_BASE_URL", self.litellm_base_url)

        # Set up other provider keys
        if self.google_api_key:
            os.environ["GOOGLE_API_KEY"] = self.google_api_key
        if self.openrouter_api_key:
            os.environ["OPENROUTER_API_KEY"] = self.openrouter_api_key
        if self.aws_access_key_id:
            os.environ["AWS_ACCESS_KEY_ID"] = self.aws_access_key_id
        if self.aws_secret_access_key:
            os.environ["AWS_SECRET_ACCESS_KEY"] = self.aws_secret_access_key
        if self.aws_region:
            os.environ["AWS_REGION"] = self.aws_region
        if self.aws_role_arn:
            os.environ["AWS_ROLE_ARN"] = self.aws_role_arn

    @property
    def parallel_processing_config(self) -> Dict[str, Any]:
        """Get parallel processing configuration as a dictionary."""
        return {
            "enabled": self.parallel_processing_enabled,
            "max_document_workers": self.max_document_workers,
            "max_file_workers": self.max_file_workers,
            "max_context_workers": self.max_context_workers,
            "context_chunk_size": self.context_chunk_size,
            "enable_performance_logging": self.enable_performance_logging,
            "min_documents_for_parallel": self.min_documents_for_parallel,
            "adaptive_worker_scaling": self.adaptive_worker_scaling,
            "performance_threshold": self.performance_threshold
        }

    class Config:
        validate_assignment = True


# Create global settings instance from environment variables
settings = DeepWikiSettings.from_env()

# Set up environment variables for backward compatibility
settings.setup_environment_variables()

# Backward compatibility exports
OPENAI_API_KEY = settings.openai_api_key
GOOGLE_API_KEY = settings.google_api_key
OPENROUTER_API_KEY = settings.openrouter_api_key
AWS_ACCESS_KEY_ID = settings.aws_access_key_id
AWS_SECRET_ACCESS_KEY = settings.aws_secret_access_key
AWS_REGION = settings.aws_region
AWS_ROLE_ARN = settings.aws_role_arn
LITELLM_CLIENT_API_KEY = settings.litellm_client_api_key
LITELLM_BASE_URL = settings.litellm_base_url
WIKI_AUTH_MODE = settings.wiki_auth_mode
WIKI_AUTH_CODE = settings.wiki_auth_code
DEFAULT_TEMPERATURE = settings.default_temperature
CONFIG_DIR = settings.config_dir
PARALLEL_PROCESSING_CONFIG = settings.parallel_processing_config

# Client class mapping
CLIENT_CLASSES = {
    "OpenAIClient": OpenAIClient,
}

def replace_env_placeholders(config: Union[Dict[str, Any], List[Any], str, Any]) -> Union[Dict[str, Any], List[Any], str, Any]:
    """
    Recursively replace placeholders like "${ENV_VAR}" in string values
    within a nested configuration structure (dicts, lists, strings)
    with environment variable values. Logs a warning if a placeholder is not found.
    """
    pattern = re.compile(r"\$\{([A-Z0-9_]+)\}")

    def replacer(match: re.Match[str]) -> str:
        env_var_name = match.group(1)
        original_placeholder = match.group(0)
        env_var_value = os.environ.get(env_var_name)
        if env_var_value is None:
            logger.warning(
                f"Environment variable placeholder '{original_placeholder}' was not found in the environment. "
                f"The placeholder string will be used as is."
            )
            return original_placeholder
        return env_var_value

    if isinstance(config, dict):
        return {k: replace_env_placeholders(v) for k, v in config.items()}
    elif isinstance(config, list):
        return [replace_env_placeholders(item) for item in config]
    elif isinstance(config, str):
        return pattern.sub(replacer, config)
    else:
        # Handles numbers, booleans, None, etc.
        return config

# Load JSON configuration file
def load_json_config(filename):
    try:
        # If environment variable is set, use the directory specified by it
        if CONFIG_DIR:
            config_path = Path(CONFIG_DIR) / filename
        else:
            # Otherwise use default directory
            config_path = Path(__file__).parent / "config" / filename

        logger.info(f"Loading configuration from {config_path}")

        if not config_path.exists():
            logger.warning(f"Configuration file {config_path} does not exist")
            return {}

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            config = replace_env_placeholders(config)
            return config
    except Exception as e:
        logger.error(f"Error loading configuration file {filename}: {str(e)}")
        return {}

# Load generator model configuration
def load_generator_config():
    generator_config = load_json_config("generator.json")

    # Add client classes to each provider
    if "providers" in generator_config:
        for provider_id, provider_config in generator_config["providers"].items():
            # Try to set client class from client_class
            if provider_config.get("client_class") in CLIENT_CLASSES:
                provider_config["model_client"] = CLIENT_CLASSES[provider_config["client_class"]]
            # Fall back to default mapping based on provider_id (only openai supported)
            elif provider_id in ["openai"]:
                default_map = {
                    "openai": OpenAIClient,
                }
                provider_config["model_client"] = default_map[provider_id]
            else:
                logger.warning(f"Unknown provider or client class: {provider_id}")

    return generator_config

# Load embedder configuration
def load_embedder_config():
    # Dynamic-only: do not load embedder.json from disk
    return {}

def get_embedder_config():
    """
    Get the current embedder configuration.

    Returns:
        dict: The embedder configuration with model_client resolved
    """
    return configs.get("embedder", {})

def is_ollama_embedder():
    """
    Check if the current embedder configuration uses OllamaClient.

    Returns:
        bool: True if using OllamaClient, False otherwise
    """
    embedder_config = get_embedder_config()
    if not embedder_config:
        return False

    # Check if model_client is OllamaClient
    model_client = embedder_config.get("model_client")
    if model_client:
        return model_client.__name__ == "OllamaClient"

    # Fallback: check client_class string
    client_class = embedder_config.get("client_class", "")
    return client_class == "OllamaClient"

# Load repository and file filters configuration
def load_repo_config():
    return load_json_config("repo.json")

# Load language configuration
def load_lang_config():
    default_config = {
        "supported_languages": {
            "en": "English",
            "ja": "Japanese (日本語)",
            "zh": "Mandarin Chinese (中文)",
            "zh-tw": "Traditional Chinese (繁體中文)",
            "es": "Spanish (Español)",
            "kr": "Korean (한국어)",
            "vi": "Vietnamese (Tiếng Việt)",
            "pt-br": "Brazilian Portuguese (Português Brasileiro)",
            "fr": "Français (French)",
            "ru": "Русский (Russian)"
        },
        "default": "en"
    }

    loaded_config = load_json_config("lang.json") # Let load_json_config handle path and loading

    if not loaded_config:
        return default_config

    if "supported_languages" not in loaded_config or "default" not in loaded_config:
        logger.warning("Language configuration file 'lang.json' is malformed. Using default language configuration.")
        return default_config

    return loaded_config

# Default excluded directories and files
DEFAULT_EXCLUDED_DIRS: List[str] = [
    # Virtual environments and package managers
    "./.venv/", "./venv/", "./env/", "./virtualenv/",
    "./node_modules/", "./bower_components/", "./jspm_packages/",
    # Version control
    "./.git/", "./.svn/", "./.hg/", "./.bzr/",
    # Cache and compiled files
    "./__pycache__/", "./.pytest_cache/", "./.mypy_cache/", "./.ruff_cache/", "./.coverage/",
    # Build and distribution
    "./dist/", "./build/", "./out/", "./target/", "./bin/", "./obj/",
    # Documentation
    "./docs/", "./_docs/", "./site-docs/", "./_site/",
    # IDE specific
    "./.idea/", "./.vscode/", "./.vs/", "./.eclipse/", "./.settings/",
    # Logs and temporary files
    "./logs/", "./log/", "./tmp/", "./temp/",
]

DEFAULT_EXCLUDED_FILES: List[str] = [
    "yarn.lock", "pnpm-lock.yaml", "npm-shrinkwrap.json", "poetry.lock",
    "Pipfile.lock", "requirements.txt.lock", "Cargo.lock", "composer.lock",
    ".lock", ".DS_Store", "Thumbs.db", "desktop.ini", "*.lnk", ".env",
    ".env.*", "*.env", "*.cfg", "*.ini", ".flaskenv", ".gitignore",
    ".gitattributes", ".gitmodules", ".github", ".gitlab-ci.yml",
    ".prettierrc", ".eslintrc", ".eslintignore", ".stylelintrc",
    ".editorconfig", ".jshintrc", ".pylintrc", ".flake8", "mypy.ini",
    "pyproject.toml", "tsconfig.json", "webpack.config.js", "babel.config.js",
    "rollup.config.js", "jest.config.js", "karma.conf.js", "vite.config.js",
    "next.config.js", "*.min.js", "*.min.css", "*.bundle.js", "*.bundle.css",
    "*.map", "*.gz", "*.zip", "*.tar", "*.tgz", "*.rar", "*.7z", "*.iso",
    "*.dmg", "*.img", "*.msix", "*.appx", "*.appxbundle", "*.xap", "*.ipa",
    "*.deb", "*.rpm", "*.msi", "*.exe", "*.dll", "*.so", "*.dylib", "*.o",
    "*.obj", "*.jar", "*.war", "*.ear", "*.jsm", "*.class", "*.pyc", "*.pyd",
    "*.pyo", "__pycache__", "*.a", "*.lib", "*.lo", "*.la", "*.slo", "*.dSYM",
    "*.egg", "*.egg-info", "*.dist-info", "*.eggs", "node_modules",
    "bower_components", "jspm_packages", "lib-cov", "coverage", "htmlcov",
    ".nyc_output", ".tox", "dist", "build", "bld", "out", "bin", "target",
    "packages/*/dist", "packages/*/build", ".output"
]

# Initialize empty configuration
configs = {}

# Load all configuration files (dynamic-only)
repo_config = load_repo_config()
lang_config = load_lang_config()

# Dynamic-only provider config: force OpenAI via LiteLLM
configs["default_provider"] = "openai"
configs["providers"] = {
    "openai": {
        "default_model": None,  # chosen by client request or UI
        "model_client": OpenAIClient,
        "models": {},  # not used, models are fetched dynamically from LiteLLM
    }
}

# Dynamic embedder + retriever + text_splitter defaults (all calls still go via LiteLLM)
EMBEDDER_MODEL = os.environ.get("EMBEDDER_MODEL", "text-embedding-3-small")
EMBEDDER_DIMENSIONS = int(os.environ.get("EMBEDDER_DIMENSIONS", "256"))
EMBEDDER_ENCODING_FORMAT = os.environ.get("EMBEDDER_ENCODING_FORMAT", "float")
configs["embedder"] = {
    "model_client": OpenAIClient,
    "model_kwargs": {
        "model": EMBEDDER_MODEL,
        "dimensions": EMBEDDER_DIMENSIONS,
        "encoding_format": EMBEDDER_ENCODING_FORMAT,
    },
}
configs["retriever"] = {"top_k": 20}
configs["text_splitter"] = {"split_by": "word", "chunk_size": 350, "chunk_overlap": 100}

# Update repository configuration
if repo_config:
    for key in ["file_filters", "repository"]:
        if key in repo_config:
            configs[key] = repo_config[key]

# Update language configuration
if lang_config:
    configs["lang_config"] = lang_config


def get_model_config(provider="openai", model=None):
    """
    Get configuration for the specified provider and model

    Parameters:
        provider (str): Model provider ('google', 'openai', 'openrouter', 'ollama', 'bedrock')
        model (str): Model name, or None to use default model

    Returns:
        dict: Configuration containing model_client, model and other parameters
    """
    # Get provider configuration (dynamic-only OpenAI via LiteLLM)
    provider_config = configs.get("providers", {}).get(provider)
    if not provider_config:
        raise ValueError(f"Configuration for provider '{provider}' not found")

    model_client = provider_config.get("model_client", OpenAIClient)

    # Select model: use provided, else default_model, else None (caller should provide)
    if not model:
        model = provider_config.get("default_model")

    # Prepare base configuration
    result = {
        "model_client": model_client,
    }

    # Standard structure for OpenAI-style providers (do not include temperature by default)
    result["model_kwargs"] = {"model": model}

    return result
