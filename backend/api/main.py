import logging
import os
import sys

import uvicorn
from dotenv import load_dotenv

# Ensure local package imports work (add project root)
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.logging_config import setup_logging  # noqa: E402


# Load environment variables from .env file
load_dotenv()

# Configure logging
setup_logging()
logger = logging.getLogger(__name__)


# Configure Google Generative AI
import google.generativeai as genai  # noqa: E402


if __name__ == "__main__":
    # Get port from environment variable or use default
    port = int(os.environ.get("PORT", 8001))

    # Import the app here to ensure environment variables are set first
    from api.api import app  

    logger.info("Starting Streaming API on port %d", port)

    # Run the FastAPI app with uvicorn
    # Disable reload in production/Docker environment
    is_development = os.environ.get("NODE_ENV") != "production"

    if is_development:
        # Prevent infinite logging loop caused by file changes triggering log writes
        logging.getLogger("watchfiles.main").setLevel(logging.WARNING)

    uvicorn.run(
        "api.api:app",
        host="0.0.0.0",
        port=port,
        reload=is_development,
    )
