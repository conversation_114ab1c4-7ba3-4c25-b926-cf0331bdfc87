version: '3.8'

tasks:
  # Docker Compose - LiteLLM
  litellm:up:
    desc: Start LiteLLM (and Postgres) via docker compose
    cmds:
      - docker compose up -d postgres litellm

  litellm:logs:
    desc: Tail logs for the litellm container
    cmds:
      - docker compose logs -f litellm

  litellm:stop:
    desc: Stop LiteLLM (and Postgres) containers
    cmds:
      - docker compose stop litellm
      - docker compose stop postgres

  # Backend - run locally
  backend:
    desc: Run backend locally with Uvicorn
    deps:
      - task: env:check
    cmds:
      - test -f .env && ln -sf ./.env ./backend/.env || echo "[Task backend] root .env not found, skipping link"
      - bash -lc 'set -a; . ./.env; set +a; uv --directory ./backend run uvicorn api.api:app --host 0.0.0.0 --port 8001'

  # Frontend - run locally
  frontend:
    desc: Run frontend locally (Next.js dev server)
    deps:
      - task: env:check
    dir: ./frontend
    cmds:
      - test -f ../.env && ln -sf ../.env ./.env || echo "[Task frontend] root .env not found, skipping link"
      - npm ci
      - npm run dev


  # Dev - start LiteLLM then run backend & frontend in parallel
  dev:
    desc: Start LiteLLM (docker) then run backend and frontend locally in parallel
    deps:
      - task: env:check
      - task: litellm:up
    cmds:
      - task: backend

  # Env - check required variables in root .env
  env:check:
    desc: Validate presence of important env vars in .env
    cmds:
      - |
        bash -c '
        set -e
        if [ ! -f .env ]; then
          echo "✗ .env not found at project root"
          exit 1
        fi
        # Load .env into environment
        set -a
        . ./.env
        set +a
        missing=0
        require_one() {
          any=0
          for key in "$@"; do
            val=$(eval echo \${$key})
            if [ -n "$val" ]; then any=1; fi
          done
          if [ $any -eq 1 ]; then
            echo "✓ One of [$*] present"
          else
            echo "✗ Need at least one of [$*]"
            missing=1
          fi
        }
        check() {
          key=$1
          val=$(eval echo \${$key})
          if [ -n "$val" ]; then echo "✓ $key set"; else echo "· $key missing (optional)"; fi
        }
        # Required alternatives
        require_one OPENAI_API_KEY LITELLM_API_KEY
        # Strongly recommended for LiteLLM proxy auth
        check PROXY_MASTER_KEY
        # Optional keys used by various providers/features
        check GOOGLE_API_KEY
        check OPENROUTER_API_KEY
        check AWS_ACCESS_KEY_ID
        check AWS_SECRET_ACCESS_KEY
        check AWS_REGION
        check AWS_ROLE_ARN
        check DEEPWIKI_AUTH_MODE
        check DEEPWIKI_AUTH_CODE
        if [ $missing -eq 1 ]; then
          echo "Some required env vars are missing"
          exit 1
        fi
        echo "All required env checks passed"
        '
