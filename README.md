## DeepWiki Engine

### Overview
DeepWiki Engine is a full‑stack system for a wiki-like experience powered by LLMs and Retrieval-Augmented Generation (RAG). The stack consists of:
- Backend: FastAPI (Python) for HTTP APIs and WebSocket streaming
- Frontend: Next.js (React)
- LLM Proxy: Li<PERSON>LLM (Docker) acting as an OpenAI‑compatible gateway for all model calls

All model and embedding calls are made with the OpenAI SDK pointing to LiteLLM. The only exposed provider is “openai” (backed by LiteLLM).

---

## High‑Level System Overview

```mermaid

flowchart LR
  subgraph FE[Frontend]
    UI[App UI]
  end

  subgraph BE[Backend: FastAPI]
    API[/HTTP Endpoints/]
    WS[/WebSocket/]
    RAG[RAG & Data Pipeline]
  end

  subgraph LLM[LiteLLM Proxy]
    LAPI[/OpenAI‑compatible API/]
    DB[(Postgres)]
  end

  subgraph Upstream[Upstream Providers]
    OAI[(OpenAI API)]
  end

  UI -- HTTP --> API
  UI -- WS --> WS
  API --> RAG
  API -- OpenAI SDK --> LAPI
  WS  -- OpenAI SDK --> LAPI
  LAPI <-- uses --> DB
  LAPI -- routes --> OAI

```

Key points:
- Frontend only talks to the Backend
- Backend uses the OpenAI SDK with OPENAI_BASE_URL set to LiteLLM
- LiteLLM proxies to upstream providers (e.g., OpenAI) and can manage keys, rate limits, logging, etc.

---

## Ports
- Frontend (Next.js): http://localhost:3000
- Backend (FastAPI): http://localhost:8001
- LiteLLM Proxy: http://localhost:4000
- Postgres (LiteLLM): 5432 (Docker)

---

## Prerequisites
- Docker + Docker Compose v2
- Node.js (v18+) and npm
- Python 3.12 and uv
- Task (task runner)
- jq (optional, useful when parsing JSON via shell)

---

## Environment Setup (.env)
Create a .env at the project root. Important variables:

Required for LiteLLM flow:
- PROXY_MASTER_KEY: Admin/master key for LiteLLM management (used to generate client keys)
- OPENAI_API_KEY: Upstream OpenAI API key (used by LiteLLM to call OpenAI)
- LITELLM_BASE_URL: Defaults to http://localhost:4000
- LITELLM_CLIENT_API_KEY: End‑user client key issued by LiteLLM (used by the backend’s OpenAI SDK)

Optional:
- DEEPWIKI_AUTH_MODE, DEEPWIKI_AUTH_CODE
- Any additional keys if you extend beyond LiteLLM routing

Notes:
- Do NOT use PROXY_MASTER_KEY as the client key for model calls
- Taskfile symlinks root .env into backend/.env and frontend/.env when running tasks

---

## LiteLLM Configuration (config.yaml)
LiteLLM reads config.yaml for model routing. At minimum, define:
- Chat model (example: openai/gpt-5-nano)
- Embeddings model for RAG (example: openai/text-embedding-3-small)

Whenever you update config.yaml, restart LiteLLM (see Tasks below).

---

## Running the Stack (Development)

1) Start LiteLLM and Postgres
- task litellm:up

2) Generate a LiteLLM end‑user client key (one‑time)
- Ensure PROXY_MASTER_KEY is present in .env
- Run the helper script:
  - python backend/examples/generate_key.py
  - Copy the printed “Key: sk-…” into .env as LITELLM_CLIENT_API_KEY

3) Start backend
- task backend
- Health check: curl http://localhost:8001/health/litellm

4) Start frontend
- task frontend
- Visit http://localhost:3000

Tip:
- You can also run task dev to bring up LiteLLM and start the backend; start the frontend in a separate terminal to keep logs readable.

---

## Tasks (Taskfile)
Convenient commands for development:
- litellm:up — Start LiteLLM and Postgres containers
- litellm:logs — Tail LiteLLM logs
- litellm:stop — Stop LiteLLM and Postgres
- env:check — Validate presence of important env vars (make sure your .env includes LITELLM_CLIENT_API_KEY for backend calls)
- backend — Run backend locally (links .env to backend/.env)
- frontend — Run frontend locally (links .env to frontend/.env, installs deps, runs dev server)
- dev — Bring up LiteLLM, then start backend

---

## Model Discovery
The backend exposes one provider (“openai”) and discovers models dynamically by calling LiteLLM:
- GET /models/config → calls OpenAI SDK’s client.models.list() against LiteLLM and returns model IDs

To add or remove models, update config.yaml for LiteLLM and restart it; the backend will reflect changes.

---

## Important Backend Endpoints
- GET /health/litellm → { ok, models_count }
- GET /models/config → Provider “openai” with models from LiteLLM
- POST /chat/completions/stream → Streaming completions
- WS /ws/chat → WebSocket streaming chat

---

## Frontend Configuration
- next.config.ts sets sensible defaults:
  - NODE_ENV defaults to development if missing
  - In development, SERVER_BASE_URL defaults to http://localhost:8001
  - Both SERVER_BASE_URL and NEXT_PUBLIC_SERVER_BASE_URL can be overridden via env
- The Taskfile links root .env to frontend/.env before starting the dev server

---

## Troubleshooting
- Missing API key error on /models/config or /health/litellm
  - Ensure LITELLM_CLIENT_API_KEY is present in .env
  - Ensure OPENAI_BASE_URL (set automatically from LITELLM_BASE_URL) points to your LiteLLM
- 401/403 from LiteLLM
  - Check PROXY_MASTER_KEY used to create the client key and validate the issued key
- models_count = 0
  - Confirm config.yaml model_list has your models and restart LiteLLM
- Frontend cannot reach backend
  - Verify SERVER_BASE_URL/NEXT_PUBLIC_SERVER_BASE_URL and ports

---

## Contributing
- Use feature branches and PRs with clear descriptions
- Add/update tests when changing backend logic
- Keep all model calls routed through the OpenAI SDK → LiteLLM to maintain a single integration point

