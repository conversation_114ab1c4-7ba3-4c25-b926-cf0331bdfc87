```mermaid
sequenceDiagram
    participant U as 👤 User
    participant WS as 📡 WebSocket
    participant API as 🔧 API Layer
    participant Cache as 🗄️ Cache System
    participant RAG as 🔍 RAG Engine
    participant DB as 📚 Database Manager
    participant PP as ⚡ Parallel Processor
    participant AI as 🤖 AI Provider
    participant FS as 📁 File System

    %% Initial Request
    U->>WS: 📝 Wiki Generation Request
    WS->>API: 🔍 Parse & Validate Request
    API->>API: 🌍 Language Detection
    API->>Cache: 🗄️ Check Wiki Cache
    
    alt Cache Hit
        Cache->>FS: 📋 Read Cached Wiki
        FS-->>Cache: 📄 Wiki Data
        Cache-->>API: ✅ Cached Wiki Found
        API-->>WS: 📋 Return Cached Wiki
        WS-->>U: 🎉 Wiki Ready (Cached)
    else Cache Miss
        Cache-->>API: ❌ No Cache Found
        
        %% Repository Processing
        API->>RAG: 🔧 Initialize RAG System
        RAG->>DB: 📚 Setup Database Manager
        DB->>DB: 🗃️ Prepare Documents
        DB-->>RAG: ✅ Documents Ready
        
        %% Parallel Document Processing
        RAG->>PP: ⚡ Start Parallel Processing
        
        par Document Processing
            PP->>PP: 🔄 Process Batch 1
        and
            PP->>PP: 🔄 Process Batch 2
        and
            PP->>PP: 🔄 Process Batch N
        end
        
        PP->>PP: 📑 Group by File Path
        
        %% Parallel Context Building
        par Context Building
            PP->>PP: 🏗️ Build Context Group 1
        and
            PP->>PP: 🏗️ Build Context Group 2
        and
            PP->>PP: 🏗️ Build Context Group N
        end
        
        PP->>PP: 📝 Aggregate Context
        PP-->>RAG: ✅ Context Ready
        
        %% File Content Retrieval (if needed)
        opt File Content Needed
            RAG->>PP: 📁 Request File Contents
            
            par File Retrieval
                PP->>FS: 📄 Get File 1
            and
                PP->>FS: 📄 Get File 2
            and
                PP->>FS: 📄 Get File N
            end
            
            FS-->>PP: 📄 File Contents
            PP-->>RAG: ✅ Files Ready
        end
        
        %% AI Processing
        RAG->>AI: 🤖 Model Configuration
        AI->>AI: 📋 Generate System Prompt
        AI->>AI: 🎯 Optimize & Cache Prompt
        
        alt Deep Research Mode
            AI->>AI: 📊 Research Plan Generation
            loop Research Iterations
                AI->>AI: 🔎 Deep Analysis
                AI->>WS: 📡 Stream Partial Results
                WS->>U: 📊 Research Progress
            end
            AI->>AI: 📋 Final Synthesis
        else Simple Chat Mode
            AI->>AI: 💬 Direct Processing
        end
        
        %% Response Generation
        AI->>WS: 📤 Stream Response
        WS->>U: 📡 Real-time Updates
        
        %% Wiki Structure Creation
        AI->>API: 📝 Wiki Content Generated
        API->>API: 🏗️ Create Wiki Structure
        API->>API: 📑 Generate Wiki Pages
        API->>API: 🔗 Link Related Pages
        API->>API: 📊 Classify Importance
        API->>API: 📂 Organize Sections
        
        %% Caching
        API->>Cache: 💾 Store Wiki Cache
        Cache->>FS: 📁 Write Cache File
        FS-->>Cache: ✅ Cache Saved
        Cache-->>API: ✅ Cache Complete
        
        %% Final Response
        API-->>WS: 🎉 Wiki Generation Complete
        WS-->>U: ✅ Wiki Ready
        
        %% Export Options
        opt Export Request
            U->>API: 📤 Export Request
            alt Markdown Export
                API->>API: 📝 Generate Markdown
                API-->>U: 📥 Download .md File
            else JSON Export
                API->>API: 📋 Generate JSON
                API-->>U: 📥 Download .json File
            end
        end
    end

    %% Performance Monitoring
    note over PP: 📊 Performance Monitoring
    PP->>PP: 📈 Track Processing Times
    PP->>PP: ⚙️ Adaptive Worker Scaling
    PP->>PP: 🎯 Optimize Future Requests
```