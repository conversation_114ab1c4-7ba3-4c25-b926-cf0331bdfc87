version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: deepwiki-backend
    ports:
      - "8001:8001"
    depends_on:
      - postgres
    env_file:
      - .env
    volumes:
      - ~/.adalflow:/root/.adalflow     
      - ./backend/api/logs:/app/api/logs
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8001/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: deepwiki-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      backend:
        condition: service_healthy

  litellm:  
      image: ghcr.io/berriai/litellm:main-stable
      container_name: litellm  
      restart: unless-stopped  
      ports:  
        - "4000:4000"  
      volumes:  
        - ./config.yaml:/app/config.yaml  
      env_file:  
        - .env  
      command:  
        - "--config=/app/config.yaml"  
        - "--port=4000"
      depends_on:
        - postgres  # Indicates that this service depends on the 'db' service, ensuring 'db' starts first
      healthcheck:  # Defines the health check configuration for the container
        test: [ "CMD-SHELL", "wget --no-verbose --tries=1 http://localhost:4000/health/liveliness || exit 1" ]  # Command to execute for health check
        interval: 30s  # Perform health check every 30 seconds
        timeout: 10s   # Health check command times out after 10 seconds
        retries: 3     # Retry up to 3 times if health check fails
        start_period: 40s  # Wait 40 seconds after container start before beginning health checks

  postgres:
    image: postgres:16
    restart: always
    container_name: litellm_db
    environment:
      POSTGRES_DB: litellm
      POSTGRES_USER: llmproxy
      POSTGRES_PASSWORD: dbpassword9090
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data # Persists Postgres data across container restarts
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -d litellm -U llmproxy"]
      interval: 1s
      timeout: 5s
      retries: 10



networks:
  default:
    driver: bridge

volumes:
  postgres_data:
    driver: local