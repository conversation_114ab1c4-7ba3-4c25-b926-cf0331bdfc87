#!/usr/bin/env python3
"""
Test script for parallel processing optimization in wiki creation.
"""

import asyncio
import time
from typing import List, Dict
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from api.websocket_wiki import (
    parallel_document_processing,
    parallel_context_building,
    parallel_file_content_retrieval,
    process_single_document,
    PARALLEL_PROCESSING_CONFIG
)

class MockDocument:
    """Mock document for testing."""
    def __init__(self, text: str, file_path: str):
        self.text = text
        self.meta_data = {'file_path': file_path}

async def test_parallel_document_processing():
    """Test parallel document processing performance with realistic workload."""
    print("🧪 Testing parallel document processing...")

    # Create realistic mock documents with substantial content
    documents = [
        MockDocument(
            f"# Module {i}\n\n" +
            f"This is a comprehensive documentation for module {i}. " * 100 +
            f"\n\n## Functions\n\n" +
            "\n".join([f"def function_{j}():\n    '''Function {j} implementation'''\n    pass\n" for j in range(10)]) +
            f"\n\n## Classes\n\n" +
            "\n".join([f"class Class{j}:\n    '''Class {j} implementation'''\n    pass\n" for j in range(5)]),
            f"module_{i//5}/file_{i}.py"
        )
        for i in range(50)  # Use more documents to see parallel benefits
    ]

    # Test with small dataset (should use sequential)
    small_docs = documents[:5]
    start_time = time.time()
    small_results = await parallel_document_processing(small_docs)
    small_time = time.time() - start_time

    # Test with large dataset (should use parallel)
    PARALLEL_PROCESSING_CONFIG["enabled"] = True
    PARALLEL_PROCESSING_CONFIG["min_documents_for_parallel"] = 10
    start_time = time.time()
    parallel_results = await parallel_document_processing(documents)
    parallel_time = time.time() - start_time

    # Test sequential processing for comparison
    PARALLEL_PROCESSING_CONFIG["enabled"] = False
    start_time = time.time()
    sequential_results = await parallel_document_processing(documents)
    sequential_time = time.time() - start_time

    # Re-enable parallel processing
    PARALLEL_PROCESSING_CONFIG["enabled"] = True

    print(f"📊 Small dataset ({len(small_docs)} docs): {small_time:.4f}s")
    print(f"📊 Large dataset sequential ({len(documents)} docs): {sequential_time:.4f}s")
    print(f"📊 Large dataset parallel ({len(documents)} docs): {parallel_time:.4f}s")

    if sequential_time > 0:
        speedup = sequential_time / parallel_time
        print(f"🚀 Speedup: {speedup:.2f}x")
    else:
        speedup = 1.0
        print(f"🚀 Processing too fast to measure speedup accurately")

    print(f"✅ Results match: {len(sequential_results) == len(parallel_results)}")

    # Consider test passed if parallel processing works correctly
    return len(parallel_results) == len(documents) and speedup >= 0.8

async def test_parallel_context_building():
    """Test parallel context building performance."""
    print("\n🧪 Testing parallel context building...")
    
    # Create mock processed documents grouped by file
    docs_by_file = {}
    for i in range(10):
        file_path = f"test_file_{i}.py"
        docs_by_file[file_path] = [
            {
                'text': f"Function {j} in file {i}\n" + "def test():\n    pass\n" * 10,
                'file_path': file_path,
                'metadata': {},
                'length': 200
            }
            for j in range(5)
        ]
    
    start_time = time.time()
    context_parts, total_length = await parallel_context_building(docs_by_file, max_context_length=8000)
    processing_time = time.time() - start_time
    
    print(f"📊 Context building time: {processing_time:.4f}s")
    print(f"📊 Generated {len(context_parts)} context parts")
    print(f"📊 Total context length: {total_length} characters")
    print(f"✅ Context parts generated: {len(context_parts) > 0}")
    
    return len(context_parts) > 0

async def test_configuration_impact():
    """Test different configuration settings."""
    print("\n🧪 Testing configuration impact...")
    
    documents = [
        MockDocument(f"Content {i} " * 100, f"file_{i}.py")
        for i in range(15)
    ]
    
    # Test with different worker counts
    worker_counts = [1, 2, 4, 8]
    times = []
    
    for workers in worker_counts:
        PARALLEL_PROCESSING_CONFIG["max_document_workers"] = workers
        start_time = time.time()
        await parallel_document_processing(documents)
        processing_time = time.time() - start_time
        times.append(processing_time)
        print(f"📊 {workers} workers: {processing_time:.4f}s")
    
    # Find optimal worker count
    optimal_idx = times.index(min(times))
    optimal_workers = worker_counts[optimal_idx]
    print(f"🎯 Optimal worker count: {optimal_workers}")
    
    return optimal_workers

async def benchmark_performance():
    """Comprehensive performance benchmark."""
    print("\n🏁 Running comprehensive performance benchmark...")
    
    # Large dataset for realistic testing
    large_documents = [
        MockDocument(
            f"# File {i}\n\n" + 
            f"This is a comprehensive test file with substantial content. " * 20 +
            f"\n\ndef function_{i}():\n    return 'test_{i}'\n" * 10,
            f"module_{i//5}/file_{i}.py"
        )
        for i in range(50)
    ]
    
    print(f"📊 Testing with {len(large_documents)} documents")
    
    # Benchmark document processing
    start_time = time.time()
    processed_docs = await parallel_document_processing(large_documents)
    doc_time = time.time() - start_time
    
    # Group documents by file path
    docs_by_file = {}
    for doc in processed_docs:
        file_path = doc.get('file_path', 'unknown')
        if file_path not in docs_by_file:
            docs_by_file[file_path] = []
        docs_by_file[file_path].append(doc)
    
    # Benchmark context building
    start_time = time.time()
    context_parts, total_length = await parallel_context_building(docs_by_file)
    context_time = time.time() - start_time
    
    print(f"📊 Document processing: {doc_time:.4f}s")
    print(f"📊 Context building: {context_time:.4f}s")
    print(f"📊 Total processing time: {doc_time + context_time:.4f}s")
    print(f"📊 Documents per second: {len(large_documents)/(doc_time + context_time):.2f}")
    print(f"📊 Final context length: {total_length} characters")
    
    return doc_time + context_time

async def main():
    """Run all tests."""
    print("🚀 Starting parallel processing tests for DeepWiki optimization\n")
    
    try:
        # Run individual tests
        test1_passed = await test_parallel_document_processing()
        test2_passed = await test_parallel_context_building()
        optimal_workers = await test_configuration_impact()
        total_time = await benchmark_performance()
        
        # Summary
        print(f"\n📋 Test Summary:")
        print(f"✅ Document processing test: {'PASSED' if test1_passed else 'FAILED'}")
        print(f"✅ Context building test: {'PASSED' if test2_passed else 'FAILED'}")
        print(f"🎯 Optimal worker configuration: {optimal_workers}")
        print(f"⚡ Total benchmark time: {total_time:.4f}s")
        
        if test1_passed and test2_passed:
            print(f"\n🎉 All tests passed! Parallel processing optimization is working correctly.")
            print(f"💡 Recommended configuration:")
            print(f"   - max_document_workers: {optimal_workers}")
            print(f"   - max_context_workers: {min(optimal_workers * 2, 8)}")
            print(f"   - enabled: True")
        else:
            print(f"\n❌ Some tests failed. Please check the implementation.")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
