{"remote": {"url": "", "branch": ""}, "output": {"file_path": "repomix-output.md", "style": "markdown", "header_text": "", "instruction_file_path": "", "remove_comments": false, "remove_empty_lines": false, "top_files_length": 5, "show_line_numbers": false, "copy_to_clipboard": false, "include_empty_directories": true, "calculate_tokens": false, "show_file_stats": false, "show_directory_structure": true, "parsable_style": false, "truncate_base64": false, "stdout": false, "include_diffs": false}, "security": {"enable_security_check": false, "exclude_suspicious_files": true}, "compression": {"enabled": true, "keep_signatures": true, "keep_docstrings": true, "keep_interfaces": true}, "ignore": {"custom_patterns": ["*.lock", "*.json", "fronend/*"], "use_gitignore": true, "use_default_ignore": true}, "include": ["backend/*"]}